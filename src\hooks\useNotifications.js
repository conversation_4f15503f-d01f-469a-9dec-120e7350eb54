import { useEffect, useState, useRef, useCallback } from 'react';
import { useAuth } from '../context/AuthContext';
import notificationService from '../services/notificationService';
import { API_CONFIG } from '../config/api';

export const useNotifications = (options = {}) => {
  const { token, isAuthenticated } = useAuth();
  const [isConnected, setIsConnected] = useState(false);
  const [isNotificationAuthenticated, setIsNotificationAuthenticated] = useState(false);
  const [notifications, setNotifications] = useState([]);
  const [connectionError, setConnectionError] = useState(null);
  const [connectionStatus, setConnectionStatus] = useState(null);

  // Use refs to store the latest token value to avoid unnecessary reconnections
  const tokenRef = useRef(token);
  const isAuthenticatedRef = useRef(isAuthenticated);
  const setupDoneRef = useRef(false);

  // Update refs when values change
  useEffect(() => {
    tokenRef.current = token;
    isAuthenticatedRef.current = isAuthenticated;
  }, [token, isAuthenticated]);

  // Function to update connection status
  const updateConnectionStatus = useCallback(() => {
    const status = notificationService.getConnectionStatus();
    setConnectionStatus(status);
    setIsConnected(status.isConnected);
    setIsNotificationAuthenticated(status.isAuthenticated);
  }, []);

  // Memoized callback handlers to prevent unnecessary re-renders
  const handleConnect = useCallback(() => {
    updateConnectionStatus();
    setConnectionError(null);
  }, [updateConnectionStatus]);

  const handleDisconnect = useCallback(() => {
    updateConnectionStatus();
  }, [updateConnectionStatus]);

  const handleAuthenticated = useCallback(() => {
    updateConnectionStatus();
    setConnectionError(null);
  }, [updateConnectionStatus]);

  const handleAuthError = useCallback((error) => {
    updateConnectionStatus();
    setConnectionError(error);
    if (options.onAuthError) {
      options.onAuthError(error);
    }
  }, [updateConnectionStatus, options]);

  const handleAnalysisComplete = useCallback((data) => {
    setNotifications(prev => [...prev, {
      id: Date.now(),
      type: 'success',
      title: 'Analysis Complete',
      message: data.message || 'Your analysis is ready!',
      data: data,
      timestamp: new Date()
    }]);

    if (options.onAnalysisComplete) {
      options.onAnalysisComplete(data);
    }
  }, [options]);

  const handleAnalysisFailed = useCallback((data) => {
    setNotifications(prev => [...prev, {
      id: Date.now(),
      type: 'error',
      title: 'Analysis Failed',
      message: data.message || 'Analysis failed. Please try again.',
      data: data,
      timestamp: new Date()
    }]);

    if (options.onAnalysisFailed) {
      options.onAnalysisFailed(data);
    }
  }, [options]);

  useEffect(() => {
    // Setup event listeners only once
    if (!setupDoneRef.current) {
      notificationService
        .onConnect(handleConnect)
        .onDisconnect(handleDisconnect)
        .onAuthenticated(handleAuthenticated)
        .onAuthError(handleAuthError)
        .onAnalysisComplete(handleAnalysisComplete)
        .onAnalysisFailed(handleAnalysisFailed);

      setupDoneRef.current = true;
    }

    // Handle connection based on auth state
    const connectToService = async () => {
      if (!isAuthenticated || !token) {
        await notificationService.disconnect();
        setIsConnected(false);
        setIsNotificationAuthenticated(false);
        setConnectionError(null);
        return;
      }

      try {
        await notificationService.connect(token, {
          url: API_CONFIG.NOTIFICATION_URL,
          ...options
        });
        // Update status after connection attempt
        updateConnectionStatus();
      } catch (error) {
        setConnectionError(error);
        updateConnectionStatus();
      }
    };

    connectToService();

    // Cleanup on unmount
    return () => {
      if (!isAuthenticatedRef.current) {
        notificationService.disconnect();
      }
    };
  }, [isAuthenticated, token, updateConnectionStatus, handleConnect, handleDisconnect, handleAuthenticated, handleAuthError, handleAnalysisComplete, handleAnalysisFailed, options]);

  const clearNotification = useCallback((notificationId) => {
    setNotifications(prev => prev.filter(n => n.id !== notificationId));
  }, []);

  const clearAllNotifications = useCallback(() => {
    setNotifications([]);
  }, []);

  const forceReconnect = useCallback(async () => {
    if (tokenRef.current && isAuthenticatedRef.current) {
      try {
        await notificationService.forceReconnect();
        setConnectionError(null);
      } catch (error) {
        setConnectionError(error);
      }
    }
  }, []);

  // Initial status update
  useEffect(() => {
    updateConnectionStatus();
  }, [updateConnectionStatus]);

  return {
    isConnected,
    isAuthenticated: isNotificationAuthenticated,
    notifications,
    connectionError,
    clearNotification,
    clearAllNotifications,
    forceReconnect,
    connectionStatus
  };
};
