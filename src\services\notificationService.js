import io from 'socket.io-client';

class NotificationService {
  constructor() {
    this.socket = null;
    this.isConnected = false;
    this.isAuthenticated = false;
    this.isConnecting = false;
    this.isDisconnecting = false;
    this.token = null;
    this.callbacks = {
      onAuthenticated: null,
      onAuthError: null,
      onAnalysisStarted: null,
      onAnalysisComplete: null,
      onAnalysisFailed: null,
      onConnect: null,
      onDisconnect: null
    };
  }

  async connect(token, options = {}) {
    // Prevent multiple simultaneous connection attempts
    if (this.isConnecting) {
      return this;
    }

    // If already connected with the same token, reuse connection
    if (this.socket && this.socket.connected && this.token === token && !this.isDisconnecting) {
      return this;
    }

    // If there's an existing socket, disconnect it properly first
    if (this.socket) {
      await this.disconnect();
    }

    // Create new connection
    this.isConnecting = true;
    try {
      const socketUrl = options.url || 'https://api.chhrone.web.id';

      this.socket = io(socketUrl, {
        autoConnect: false,
        transports: ['websocket', 'polling'],
        reconnection: true,
        reconnectionDelay: 1000,
        reconnectionAttempts: 5,
        timeout: 20000,
        forceNew: true,
        ...options.socketOptions
      });

      this.token = token;
      this.setupEventListeners();

      // Connect and wait for connection to establish
      await new Promise((resolve, reject) => {
        const connectTimeout = setTimeout(() => {
          reject(new Error('Connection timeout'));
        }, 10000);

        this.socket.once('connect', () => {
          clearTimeout(connectTimeout);
          resolve();
        });

        this.socket.once('connect_error', (error) => {
          clearTimeout(connectTimeout);
          reject(error);
        });

        this.socket.connect();
      });

    } catch (error) {
      this.socket = null;
      this.token = null;
    } finally {
      this.isConnecting = false;
    }

    return this;
  }

  async disconnect() {
    if (this.isDisconnecting) {
      return;
    }

    if (!this.socket) {
      return;
    }

    this.isDisconnecting = true;

    try {
      // Wait for proper disconnection
      await new Promise((resolve) => {
        const disconnectTimeout = setTimeout(() => {
          resolve();
        }, 3000);

        if (this.socket.connected) {
          this.socket.once('disconnect', () => {
            clearTimeout(disconnectTimeout);
            resolve();
          });
          this.socket.disconnect();
        } else {
          clearTimeout(disconnectTimeout);
          resolve();
        }
      });

      // Clean up
      if (this.socket) {
        this.socket.removeAllListeners();
        this.socket = null;
      }

      this.token = null;
      this.isConnected = false;
      this.isAuthenticated = false;

    } catch (error) {
      // Silent error handling
    } finally {
      this.isDisconnecting = false;
    }
  }

  authenticate() {
    if (this.socket && this.socket.connected && this.token) {
      this.socket.emit('authenticate', { token: this.token });
    }
  }

  setupEventListeners() {
    if (!this.socket) return;

    // Connection events
    this.socket.on('connect', () => {
      this.isConnected = true;
      this.authenticate();
      this.callbacks.onConnect?.();
    });

    this.socket.on('disconnect', (reason) => {
      this.isConnected = false;
      this.isAuthenticated = false;
      this.callbacks.onDisconnect?.();
    });

    this.socket.on('connect_error', (error) => {
      this.isConnected = false;
      this.isAuthenticated = false;
    });

    this.socket.on('reconnect', (attemptNumber) => {
      this.isConnected = true;
      this.authenticate();
    });

    this.socket.on('reconnect_attempt', (attemptNumber) => {
      // Silent reconnection attempt
    });

    this.socket.on('reconnect_error', (error) => {
      // Silent reconnection error
    });

    this.socket.on('reconnect_failed', () => {
      this.isConnected = false;
      this.isAuthenticated = false;
    });

    // Authentication events
    this.socket.on('authenticated', (data) => {
      this.isAuthenticated = true;
      this.callbacks.onAuthenticated?.(data);
    });

    this.socket.on('auth_error', (error) => {
      this.isAuthenticated = false;
      this.callbacks.onAuthError?.(error);
    });

    // Notification events
    this.socket.on('analysis-started', (data) => {
      this.callbacks.onAnalysisStarted?.(data);
    });

    this.socket.on('analysis-complete', (data) => {
      this.callbacks.onAnalysisComplete?.(data);
    });

    this.socket.on('analysis-failed', (data) => {
      this.callbacks.onAnalysisFailed?.(data);
    });
  }

  // Callback setters
  onAuthenticated(callback) {
    this.callbacks.onAuthenticated = callback;
    return this;
  }

  onAuthError(callback) {
    this.callbacks.onAuthError = callback;
    return this;
  }

  onAnalysisStarted(callback) {
    this.callbacks.onAnalysisStarted = callback;
    return this;
  }

  onAnalysisComplete(callback) {
    this.callbacks.onAnalysisComplete = callback;
    return this;
  }

  onAnalysisFailed(callback) {
    this.callbacks.onAnalysisFailed = callback;
    return this;
  }

  onConnect(callback) {
    this.callbacks.onConnect = callback;
    return this;
  }

  onDisconnect(callback) {
    this.callbacks.onDisconnect = callback;
    return this;
  }

  // Status getters
  getConnectionStatus() {
    return {
      isConnected: this.isConnected,
      isAuthenticated: this.isAuthenticated,
      isConnecting: this.isConnecting,
      isDisconnecting: this.isDisconnecting,
      socketId: this.socket?.id,
      hasToken: !!this.token,
      socketConnected: this.socket?.connected || false,
      transport: this.socket?.io?.engine?.transport?.name || 'unknown'
    };
  }

  // Force reconnection method
  async forceReconnect() {
    const token = this.token;
    await this.disconnect();
    if (token) {
      await this.connect(token);
    }
  }
}

// Create singleton instance
const notificationService = new NotificationService();

export default notificationService;
